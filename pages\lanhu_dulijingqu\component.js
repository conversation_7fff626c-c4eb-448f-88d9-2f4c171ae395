const apiService = require('../../utils/apiService');

Page({
  data: {
    scenicId: '',           // 景区ID
    scenicDetail: null,     // 景区详情数据
    commentaryProducts: [], // 讲解产品列表
    reviews: {              // 评价数据
      list: [],
      total: 0,
      page: 1,
      limit: 10
    },
    loading: true,          // 加载状态
    error: false,           // 错误状态
    errorMessage: ''        // 错误信息
  },

  onLoad: function(options) {
    console.log('景区详情页面加载');
    console.log('页面参数:', options);
    this.initPage(options);
  },

  onShow: function() {
    console.log('景区详情页面显示');
  },

  onHide: function() {
    console.log('景区详情页面隐藏');
  },

  onUnload: function() {
    console.log('景区详情页面卸载');
  },
    // 初始化页面
    initPage: function(options) {
      try {
        console.log('初始化页面，参数:', options);

        const scenicId = options.scenicId;
        if (!scenicId) {
          console.error('景区ID参数缺失');
          this.setData({
            loading: false,
            error: true,
            errorMessage: '景区ID参数缺失'
          });
          wx.showToast({
            title: '参数错误',
            icon: 'none'
          });
          return;
        }

        this.setData({
          scenicId: scenicId
        });

        // 加载景区详情和相关数据
        this.loadScenicDetail(scenicId);
        this.loadCommentaryProducts(scenicId);
        this.loadScenicReviews(scenicId);
      } catch (error) {
        console.error('初始化页面失败:', error);
        this.setData({
          loading: false,
          error: true,
          errorMessage: '页面初始化失败'
        });
      }
    },

    // 加载景区详情
    async loadScenicDetail(scenicId) {
      try {
        console.log('开始加载景区详情:', scenicId);

        this.setData({
          loading: true,
          error: false
        });

        const scenicDetail = await apiService.getScenicDetail(scenicId);

        console.log('景区详情加载成功:', scenicDetail);

        // 处理images字段（JSON字符串转数组）
        if (scenicDetail.images && typeof scenicDetail.images === 'string') {
          try {
            scenicDetail.imageList = JSON.parse(scenicDetail.images);
            console.log('解析图片列表成功:', scenicDetail.imageList);
          } catch (error) {
            console.error('解析图片列表失败:', error);
            scenicDetail.imageList = [];
          }
        } else if (Array.isArray(scenicDetail.images)) {
          scenicDetail.imageList = scenicDetail.images;
        } else {
          scenicDetail.imageList = [];
        }

        this.setData({
          scenicDetail: scenicDetail,
          loading: false,
          error: false
        });

      } catch (error) {
        console.error('加载景区详情失败:', error);

        this.setData({
          loading: false,
          error: true,
          errorMessage: error.message || '加载景区详情失败'
        });

        wx.showToast({
          title: '加载失败',
          icon: 'none'
        });
      }
    },



    // 返回上一页
    onGoBack: function() {
      wx.navigateBack({
        delta: 1
      });
    },

    // 分享功能
    onShare: function() {
      const scenicDetail = this.data.scenicDetail;
      if (scenicDetail) {
        wx.showShareMenu({
          withShareTicket: true,
          menus: ['shareAppMessage', 'shareTimeline']
        });
      }
    },

    // 收藏功能
    onFavorite: function() {
      // 这里可以实现收藏功能
      wx.showToast({
        title: '收藏功能待实现',
        icon: 'none'
      });
    },

    // 联系电话
    onCallPhone: function() {
      const scenicDetail = this.data.scenicDetail;
      if (scenicDetail && scenicDetail.phone) {
        wx.makePhoneCall({
          phoneNumber: scenicDetail.phone,
          success: () => {
            console.log('拨打电话成功');
          },
          fail: (err) => {
            console.error('拨打电话失败:', err);
            wx.showToast({
              title: '拨打电话失败',
              icon: 'none'
            });
          }
        });
      } else {
        wx.showToast({
          title: '暂无联系电话',
          icon: 'none'
        });
      }
    },

    // 查看位置
    onViewLocation: function() {
      const scenicDetail = this.data.scenicDetail;
      if (scenicDetail && scenicDetail.latitude && scenicDetail.longitude) {
        wx.openLocation({
          latitude: parseFloat(scenicDetail.latitude),
          longitude: parseFloat(scenicDetail.longitude),
          name: scenicDetail.title || '景区位置',
          address: scenicDetail.address || '',
          success: () => {
            console.log('打开位置成功');
          },
          fail: (err) => {
            console.error('打开位置失败:', err);
            wx.showToast({
              title: '打开位置失败',
              icon: 'none'
            });
          }
        });
      } else {
        wx.showToast({
          title: '暂无位置信息',
          icon: 'none'
        });
      }
    },

    // 加载讲解产品
    async loadCommentaryProducts(scenicId) {
      try {
        console.log('开始加载讲解产品:', scenicId);

        const products = await apiService.getCommentaryProducts(scenicId);

        console.log('讲解产品加载成功:', products);

        this.setData({
          commentaryProducts: products || []
        });

      } catch (error) {
        console.error('加载讲解产品失败:', error);
        // 讲解产品加载失败时不显示错误提示，使用空数组
        this.setData({
          commentaryProducts: []
        });
      }
    },

    // 加载景区评价
    async loadScenicReviews(scenicId) {
      try {
        console.log('开始加载景区评价:', scenicId);

        const reviewsData = await apiService.getScenicReviews(scenicId, {
          page: 1,
          limit: 5  // 只加载前5条评价
        });

        console.log('景区评价加载成功:', reviewsData);

        this.setData({
          reviews: reviewsData || { list: [], total: 0, page: 1, limit: 5 }
        });

      } catch (error) {
        console.error('加载景区评价失败:', error);
        // 评价加载失败时不显示错误提示，使用空数据
        this.setData({
          reviews: { list: [], total: 0, page: 1, limit: 5 }
        });
      }
    },

    // 重新加载所有数据
    onRetryLoad: function() {
      if (this.data.scenicId) {
        this.loadScenicDetail(this.data.scenicId);
        this.loadCommentaryProducts(this.data.scenicId);
        this.loadScenicReviews(this.data.scenicId);
      }
    }
});
