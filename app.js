// app.js
App({
  onLaunch() {
    wx.loadFontFace({
      family: 'PingFang SC-Medium',
      source: 'url("https://cos.gobsweb.com/tourism/files/font/pingfang%20sc%20medium.otf")',
      global: true,
      success(res) {
        console.log('PingFang SC-Medium字体加载成功：', res);
      },
      fail(err) {
        console.error('PingFang SC-Medium字体加载失败：', err);
      },
    });
    wx.loadFontFace({
      family: 'PingFang SC-Regular',
      source: 'url("https://cos.gobsweb.com/tourism/files/font/PingFang%20Regular.otf")',
      global: true,
      success(res) {
        console.log('PingFang SC-Medium字体加载成功：', res);
      },
      fail(err) {
        console.error('PingFang SC-Medium字体加载失败：', err);
      },
    });
  },
  globalData: {
    userInfo: null,
    baseUrl: 'http://localhost:8080'
  }
})
