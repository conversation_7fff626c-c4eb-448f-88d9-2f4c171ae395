const apiService = require('../../utils/apiService.js');

Component({
  properties: {},
  data: {
    scenicId: '',
    province: '',
    title: '',
    productId: '',           // 讲解产品ID
    productDetail: null,     // 讲解产品详情数据
    scenicInfo: null,        // 景区信息（保持兼容性）
    loading: true,           // 加载状态
    error: false,            // 错误状态
    errorMessage: ''         // 错误信息
  },
  lifetimes: {
    created: function () {},
    attached: function () {
      console.info("景区详情页面加载");
      this.getPageParams();
    },
    detached: function () {
      console.info("景区详情页面卸载");
    },
  },
  methods: {
    // 获取页面参数
    getPageParams: function() {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options;

      console.log('接收到的页面参数:', options);

      this.setData({
        scenicId: options.scenicId || '',
        province: options.province || '',
        title: decodeURIComponent(options.title || ''),
        productId: options.productId || ''
      });

      // 根据参数类型加载相应数据
      if (this.data.productId) {
        // 如果有productId，加载讲解产品详情
        this.loadProductDetail();
      } else {
        // 否则加载景区详细信息（保持兼容性）
        this.loadScenicInfo();
      }
    },

    // 加载景区详细信息
    loadScenicInfo: function() {
      // 这里可以根据scenicId从服务器获取详细信息
      // 目前使用模拟数据
      const mockScenicInfo = {
        id: this.data.scenicId,
        name: this.data.title,
        province: this.data.province,
        description: `这里是${this.data.title}的详细介绍...`,
        images: [],
        rating: 4.8,
        price: '免费',
        openTime: '08:00-18:00'
      };

      this.setData({
        scenicInfo: mockScenicInfo
      });

      console.log('景区信息加载完成:', mockScenicInfo);
    }
  },
});
