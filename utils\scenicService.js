// 景区服务模块
const httpService = require('./httpService');

class ScenicService {
  constructor() {
    this.cacheKeyPrefix = 'scenics_cache_';
    this.cacheTime = 2 * 60 * 1000; // 2分钟缓存
  }

  // 获取推荐景区列表
  async getRecommendScenics(params = {}) {
    try {
      const {
        province_id,
        city_id,
        page = 1,
        limit = 10
      } = params;

      // 构建缓存key
      const cacheKey = this.buildCacheKey('recommend', params);
      
      // 检查缓存
      const cachedData = this.getCache(cacheKey);
      if (cachedData) {
        console.log('使用缓存的推荐景区数据');
        return cachedData;
      }

      console.log('从服务器获取推荐景区数据');
      const queryParams = {
        page: page.toString(),
        limit: limit.toString()
      };

      if (province_id) {
        queryParams.province_id = province_id.toString();
      }

      if (city_id && city_id !== 0) {
        queryParams.city_id = city_id.toString();
      }

      const scenics = await httpService.get('/api/scenics/recommend', queryParams, {
        loadingText: '加载景区中...'
      });

      // 缓存数据
      this.setCache(cacheKey, scenics);
      
      return scenics;
    } catch (error) {
      console.error('获取推荐景区失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 获取景区详情
  async getScenicDetail(scenicId) {
    try {
      if (!scenicId) {
        throw new Error('景区ID不能为空');
      }

      const detail = await httpService.get(`/api/scenics/${scenicId}`, {}, {
        loadingText: '加载详情中...'
      });

      return detail;
    } catch (error) {
      console.error('获取景区详情失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 搜索景区
  async searchScenics(keyword, params = {}) {
    try {
      if (!keyword) {
        throw new Error('搜索关键词不能为空');
      }

      const {
        province_id,
        city_id,
        page = 1,
        limit = 10
      } = params;

      const queryParams = {
        keyword: keyword,
        page: page.toString(),
        limit: limit.toString()
      };

      if (province_id) {
        queryParams.province_id = province_id.toString();
      }

      if (city_id && city_id !== 0) {
        queryParams.city_id = city_id.toString();
      }

      const scenics = await httpService.get('/api/scenics/search', queryParams, {
        loadingText: '搜索中...'
      });

      return scenics;
    } catch (error) {
      console.error('搜索景区失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 根据省份ID获取景区列表
  async getScenicsByProvince(provinceId, params = {}) {
    try {
      if (!provinceId) {
        throw new Error('省份ID不能为空');
      }

      const {
        page = 1,
        limit = 20
      } = params;

      // 构建缓存key
      const cacheKey = this.buildCacheKey('province', { provinceId, page, limit });

      // 检查缓存
      const cachedData = this.getCache(cacheKey);
      if (cachedData) {
        console.log('使用缓存的省份景区数据');
        return cachedData;
      }

      console.log(`从服务器获取省份景区数据 (省份ID: ${provinceId})`);
      const queryParams = {
        page: page.toString(),
        limit: limit.toString()
      };

      const scenics = await httpService.get(`/api/scenics/province/${provinceId}`, queryParams, {
        loadingText: '加载景区中...'
      });

      // 缓存数据
      this.setCache(cacheKey, scenics);

      return scenics;
    } catch (error) {
      console.error('获取省份景区失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 根据城市ID获取景区列表
  async getScenicsByCity(cityId, params = {}) {
    try {
      if (!cityId) {
        throw new Error('城市ID不能为空');
      }

      const {
        page = 1,
        limit = 20
      } = params;

      // 构建缓存key
      const cacheKey = this.buildCacheKey('city', { cityId, page, limit });

      // 检查缓存
      const cachedData = this.getCache(cacheKey);
      if (cachedData) {
        console.log('使用缓存的城市景区数据');
        return cachedData;
      }

      console.log(`从服务器获取城市景区数据 (城市ID: ${cityId})`);
      const queryParams = {
        page: page.toString(),
        limit: limit.toString()
      };

      const scenics = await httpService.get(`/api/scenics/city/${cityId}`, queryParams, {
        loadingText: '加载景区中...'
      });

      // 缓存数据
      this.setCache(cacheKey, scenics);

      return scenics;
    } catch (error) {
      console.error('获取城市景区失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 根据景区ID获取景区详情
  async getScenicDetail(scenicId) {
    try {
      if (!scenicId) {
        throw new Error('景区ID不能为空');
      }

      // 构建缓存key
      const cacheKey = this.buildCacheKey('detail', { scenicId });

      // 检查缓存
      const cachedData = this.getCache(cacheKey);
      if (cachedData) {
        console.log('使用缓存的景区详情数据');
        return cachedData;
      }

      console.log(`从服务器获取景区详情 (景区ID: ${scenicId})`);
      const scenicDetail = await httpService.get(`/api/scenics/${scenicId}`, {}, {
        loadingText: '加载景区详情中...'
      });

      // 处理返回的数据结构
      let detailData = null;
      if (scenicDetail && scenicDetail.data) {
        detailData = scenicDetail.data;
      } else if (scenicDetail) {
        detailData = scenicDetail;
      }

      if (!detailData) {
        throw new Error('景区详情数据为空');
      }

      // 缓存数据
      this.setCache(cacheKey, detailData);

      console.log('景区详情获取成功:', detailData.title || detailData.scenicId);
      return detailData;
    } catch (error) {
      console.error('获取景区详情失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 获取热门景区
  async getHotScenics(params = {}) {
    try {
      const {
        province_id,
        city_id,
        limit = 10
      } = params;

      const queryParams = { limit: limit.toString() };

      if (province_id) {
        queryParams.province_id = province_id.toString();
      }

      if (city_id && city_id !== 0) {
        queryParams.city_id = city_id.toString();
      }

      const scenics = await httpService.get('/api/scenics/hot', queryParams, {
        showLoading: false
      });

      return scenics;
    } catch (error) {
      console.error('获取热门景区失败:', error);
      // 热门景区失败时不显示错误提示
      throw error;
    }
  }

  // 获取景区分类
  async getScenicCategories() {
    try {
      const categories = await httpService.get('/api/scenics/categories', {}, {
        showLoading: false
      });

      return categories;
    } catch (error) {
      console.error('获取景区分类失败:', error);
      throw error;
    }
  }

  // 根据分类获取景区
  async getScenicsByCategory(categoryId, params = {}) {
    try {
      if (!categoryId) {
        throw new Error('分类ID不能为空');
      }

      const {
        province_id,
        city_id,
        page = 1,
        limit = 10
      } = params;

      const queryParams = {
        category_id: categoryId.toString(),
        page: page.toString(),
        limit: limit.toString()
      };

      if (province_id) {
        queryParams.province_id = province_id.toString();
      }

      if (city_id && city_id !== 0) {
        queryParams.city_id = city_id.toString();
      }

      const scenics = await httpService.get('/api/scenics/category', queryParams, {
        loadingText: '加载景区中...'
      });

      return scenics;
    } catch (error) {
      console.error('获取分类景区失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 缓存相关方法
  buildCacheKey(type, params) {
    const paramsStr = Object.keys(params)
      .sort()
      .map(key => `${key}=${params[key]}`)
      .join('&');
    return `${this.cacheKeyPrefix}${type}_${paramsStr}`;
  }

  getCache(cacheKey) {
    try {
      const cached = wx.getStorageSync(cacheKey);
      if (cached && cached.timestamp) {
        const now = Date.now();
        if (now - cached.timestamp < this.cacheTime) {
          return cached.data;
        } else {
          // 缓存过期，清除
          wx.removeStorageSync(cacheKey);
        }
      }
    } catch (error) {
      console.error('读取景区缓存失败:', error);
    }
    return null;
  }

  setCache(cacheKey, data) {
    try {
      wx.setStorageSync(cacheKey, {
        data: data,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('设置景区缓存失败:', error);
    }
  }

  clearCache(pattern = null) {
    try {
      const storage = wx.getStorageInfoSync();
      storage.keys.forEach(key => {
        if (key.startsWith(this.cacheKeyPrefix)) {
          if (!pattern || key.includes(pattern)) {
            wx.removeStorageSync(key);
          }
        }
      });
      console.log('景区缓存已清除');
    } catch (error) {
      console.error('清除景区缓存失败:', error);
    }
  }

  // 验证景区数据
  validateScenic(scenic) {
    if (!scenic) {
      return false;
    }

    const requiredFields = ['id', 'scenic_id', 'title'];
    return requiredFields.every(field => scenic.hasOwnProperty(field) && scenic[field]);
  }

  // 格式化景区数据用于显示
  formatScenicForDisplay(scenic) {
    if (!this.validateScenic(scenic)) {
      return null;
    }

    return {
      id: scenic.id,
      scenicId: scenic.scenic_id,
      title: scenic.title,
      subtitle: scenic.subtitle || '',
      description: scenic.description || '',
      image: scenic.image || '',
      price: scenic.price || 0,
      rating: scenic.rating || 0,
      provinceId: scenic.province_id,
      cityId: scenic.city_id,
      status: scenic.status || 1
    };
  }

  // 批量格式化景区数据
  formatScenicListForDisplay(scenicData) {
    if (!scenicData) {
      return { list: [], total: 0, page: 1, limit: 10 };
    }

    // 处理不同的数据结构
    let list = [];
    let total = 0;
    let page = 1;
    let limit = 10;

    if (Array.isArray(scenicData)) {
      // 直接是数组
      list = scenicData;
      total = scenicData.length;
    } else if (scenicData.list && Array.isArray(scenicData.list)) {
      // 分页数据结构
      list = scenicData.list;
      total = scenicData.total || scenicData.list.length;
      page = scenicData.page || 1;
      limit = scenicData.limit || 10;
    }

    const formattedList = list
      .map(scenic => this.formatScenicForDisplay(scenic))
      .filter(scenic => scenic !== null);

    return {
      list: formattedList,
      total,
      page,
      limit
    };
  }
}

// 创建单例实例
const scenicService = new ScenicService();

module.exports = scenicService;
